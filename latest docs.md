# 🚀 Exam WebSocket Documentation

## Overview
This document provides complete WebSocket integration for seamless exam attempts with real-time monitoring, auto-save, and security features.

## 📋 Quick Start Guide

### Step 1: Get Exam Session
```bash
# 1. Request session ID
POST /api/exams/session/exam-session/request/{exam_id}
→ Returns: {"session_id": "uuid"}

# 2. Get exam details for attempt
GET /api/exams/session/attempt/{session_id}
→ Returns: Complete exam with questions (NO ANSWERS)
```

### Step 2: Connect WebSocket
```javascript
const ws = new WebSocket(`ws://localhost:8000/api/ws/exam-session/${session_id}?token=${jwt_token}`);
```

## 🔌 WebSocket Connection

### Connection URL
```
ws://localhost:8000/api/ws/exam-session/{session_id}?token={jwt_token}
```

**Alternative URL** (both work):
```
ws://localhost:8000/api/exams/session/ws/exam-session/{session_id}?token={jwt_token}
```

### Authentication
- **Required**: JWT token as query parameter
- **Validation**: Session ownership and active status
- **Auto-disconnect**: Invalid sessions are immediately disconnected

## 📨 Message Protocol

### 1. Connection Events

#### On Connect
```json
{
  "type": "connection_established",
  "session_id": "uuid",
  "student_id": "uuid",
  "exam_id": "uuid",
  "remaining_time": 3600,
  "timestamp": "2025-09-17T12:00:00Z"
}
```

#### On Disconnect
```json
{
  "type": "connection_lost",
  "reason": "client_disconnect|network_error|server_shutdown",
  "auto_submit_triggered": true,
  "timestamp": "2025-09-17T12:30:00Z"
}
```

### 2. Answer Submission

#### Save Answer (Client → Server)
```json
{
  "type": "save_answer",
  "question_id": "uuid",
  "answer": "student_answer_text",
  "question_type": "MCQS|SHORT|LONG",
  "timestamp": "2025-09-17T12:15:00Z"
}
```

#### Answer Saved (Server → Client)
```json
{
  "type": "answer_saved",
  "question_id": "uuid",
  "status": "success|error",
  "message": "Answer saved successfully",
  "timestamp": "2025-09-17T12:15:01Z"
}
```

### 3. Heartbeat & Monitoring

#### Heartbeat (Client → Server)
```json
{
  "type": "heartbeat",
  "timestamp": "2025-09-17T12:20:00Z"
}
```

#### Heartbeat Response (Server → Client)
```json
{
  "type": "heartbeat_ack",
  "remaining_time": 2400,
  "session_status": "active",
  "timestamp": "2025-09-17T12:20:01Z"
}
```

### 4. Security & Cheating Detection

#### Tab Switch Detection (Client → Server)
```json
{
  "type": "tab_switch",
  "event": "focus_lost|focus_gained",
  "timestamp": "2025-09-17T12:25:00Z"
}
```

#### Security Warning (Server → Client)
```json
{
  "type": "security_warning",
  "violation_type": "tab_switch|copy_paste|right_click",
  "strike_count": 1,
  "max_strikes": 3,
  "message": "Warning: Tab switching detected. Strike 1/3",
  "timestamp": "2025-09-17T12:25:01Z"
}
```

#### Disqualification (Server → Client)
```json
{
  "type": "disqualified",
  "reason": "max_strikes_reached|suspicious_activity",
  "final_strike_count": 3,
  "auto_submit_triggered": true,
  "timestamp": "2025-09-17T12:30:00Z"
}
```

### 5. Time Management

#### Time Warning (Server → Client)
```json
{
  "type": "time_warning",
  "remaining_time": 300,
  "warning_type": "5_minutes|1_minute|30_seconds",
  "message": "5 minutes remaining",
  "timestamp": "2025-09-17T12:55:00Z"
}
```

#### Time Expired (Server → Client)
```json
{
  "type": "time_expired",
  "auto_submit_triggered": true,
  "final_answers_count": 15,
  "timestamp": "2025-09-17T13:00:00Z"
}
```

### 6. Manual Submission

#### Submit Exam (Client → Server)
```json
{
  "type": "submit_exam",
  "confirm": true,
  "timestamp": "2025-09-17T12:45:00Z"
}
```

#### Submission Confirmed (Server → Client)
```json
{
  "type": "exam_submitted",
  "status": "success",
  "submission_time": "2025-09-17T12:45:01Z",
  "answers_submitted": 15,
  "session_ended": true
}
```

## 🛡️ Security Features

### 1. Cheating Detection
- **Tab Switching**: Monitors focus events
- **Copy/Paste**: Detects clipboard operations
- **Right Click**: Prevents context menu access
- **Multiple Windows**: Detects new window/tab creation
- **Strike System**: 3 strikes = automatic disqualification

### 2. Auto-Submit Triggers
- **Time Expiry**: Automatic submission when time runs out
- **Disqualification**: Auto-submit after 3 strikes
- **Connection Loss**: Auto-submit on WebSocket disconnect
- **Browser Close**: Auto-submit on page unload

### 3. Data Protection
- **No Answer Exposure**: Exam data never contains correct answers
- **Session Validation**: Continuous session ownership checks
- **Encrypted Communication**: WebSocket over WSS in production
- **Audit Logging**: All activities logged to MongoDB

## 💻 Frontend Implementation

### JavaScript Example
```javascript
class ExamWebSocket {
  constructor(sessionId, token) {
    this.sessionId = sessionId;
    this.token = token;
    this.ws = null;
    this.heartbeatInterval = null;
    this.answers = {};
  }

  connect() {
    const wsUrl = `ws://localhost:8000/api/exams/session/ws/exam-session/${this.sessionId}?token=${this.token}`;
    this.ws = new WebSocket(wsUrl);
    
    this.ws.onopen = this.onOpen.bind(this);
    this.ws.onmessage = this.onMessage.bind(this);
    this.ws.onclose = this.onClose.bind(this);
    this.ws.onerror = this.onError.bind(this);
  }

  onOpen(event) {
    console.log('Connected to exam session');
    this.startHeartbeat();
    this.setupSecurityMonitoring();
  }

  onMessage(event) {
    const message = JSON.parse(event.data);
    
    switch(message.type) {
      case 'connection_established':
        this.handleConnectionEstablished(message);
        break;
      case 'answer_saved':
        this.handleAnswerSaved(message);
        break;
      case 'security_warning':
        this.handleSecurityWarning(message);
        break;
      case 'time_warning':
        this.handleTimeWarning(message);
        break;
      case 'time_expired':
        this.handleTimeExpired(message);
        break;
      case 'disqualified':
        this.handleDisqualification(message);
        break;
      case 'heartbeat_ack':
        this.handleHeartbeat(message);
        break;
    }
  }

  saveAnswer(questionId, answer, questionType) {
    const message = {
      type: 'save_answer',
      question_id: questionId,
      answer: answer,
      question_type: questionType,
      timestamp: new Date().toISOString()
    };
    
    this.ws.send(JSON.stringify(message));
    this.answers[questionId] = answer;
  }

  submitExam() {
    const message = {
      type: 'submit_exam',
      confirm: true,
      timestamp: new Date().toISOString()
    };
    
    this.ws.send(JSON.stringify(message));
  }

  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws.readyState === WebSocket.OPEN) {
        const message = {
          type: 'heartbeat',
          timestamp: new Date().toISOString()
        };
        this.ws.send(JSON.stringify(message));
      }
    }, 30000); // Every 30 seconds
  }

  setupSecurityMonitoring() {
    // Tab switch detection
    window.addEventListener('blur', () => {
      this.sendSecurityEvent('tab_switch', 'focus_lost');
    });
    
    window.addEventListener('focus', () => {
      this.sendSecurityEvent('tab_switch', 'focus_gained');
    });
    
    // Prevent right click
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      this.sendSecurityEvent('right_click', 'attempted');
    });
    
    // Prevent copy/paste
    document.addEventListener('copy', (e) => {
      e.preventDefault();
      this.sendSecurityEvent('copy_paste', 'copy_attempted');
    });
    
    document.addEventListener('paste', (e) => {
      e.preventDefault();
      this.sendSecurityEvent('copy_paste', 'paste_attempted');
    });
  }

  sendSecurityEvent(violationType, event) {
    const message = {
      type: violationType,
      event: event,
      timestamp: new Date().toISOString()
    };
    
    this.ws.send(JSON.stringify(message));
  }

  handleSecurityWarning(message) {
    alert(`Security Warning: ${message.message}`);
    // Update UI to show strike count
    document.getElementById('strike-count').textContent = 
      `Strikes: ${message.strike_count}/${message.max_strikes}`;
  }

  handleTimeWarning(message) {
    // Show time warning to user
    const minutes = Math.floor(message.remaining_time / 60);
    alert(`Time Warning: ${minutes} minutes remaining!`);
  }

  handleTimeExpired(message) {
    alert('Time expired! Your exam has been automatically submitted.');
    window.location.href = '/exam-completed';
  }

  handleDisqualification(message) {
    alert(`You have been disqualified: ${message.reason}`);
    window.location.href = '/exam-disqualified';
  }

  onClose(event) {
    console.log('WebSocket connection closed');
    clearInterval(this.heartbeatInterval);
    
    // Show reconnection UI or redirect
    if (!this.intentionalClose) {
      alert('Connection lost. Your exam has been auto-submitted.');
    }
  }

  disconnect() {
    this.intentionalClose = true;
    if (this.ws) {
      this.ws.close();
    }
    clearInterval(this.heartbeatInterval);
  }
}

// Usage
const examWS = new ExamWebSocket(sessionId, jwtToken);
examWS.connect();
```

## 🔧 Error Handling

### Connection Errors
- **Invalid Session**: WebSocket closes with code 4001
- **Expired Session**: WebSocket closes with code 4002  
- **Authentication Failed**: WebSocket closes with code 4003
- **Server Error**: WebSocket closes with code 1011

### Recovery Strategies
- **Auto-Reconnect**: Attempt reconnection with exponential backoff
- **Local Storage**: Save answers locally as backup
- **Graceful Degradation**: Fall back to HTTP polling if WebSocket fails

## 📊 Monitoring & Analytics

### Real-time Metrics
- Active exam sessions
- Answer submission rate
- Security violations
- Connection stability
- Time utilization

### Audit Trail
- All WebSocket messages logged
- Student activity timeline
- Security event tracking
- Performance metrics

## 🚀 Production Considerations

### Security
- Use WSS (WebSocket Secure) in production
- Implement rate limiting
- Add CSRF protection
- Monitor for DDoS attacks

### Performance
- Connection pooling
- Message compression
- Horizontal scaling with Redis
- Load balancing

### Reliability
- Health checks
- Graceful shutdowns
- Circuit breakers
- Failover mechanisms

---

## 📞 Support

For technical support or questions about the WebSocket implementation, please contact the development team or refer to the API documentation.
