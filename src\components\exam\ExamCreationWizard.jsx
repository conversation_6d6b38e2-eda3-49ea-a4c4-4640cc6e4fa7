import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useThemeProvider } from '../../providers/ThemeContext';
import { useNotification } from '../../contexts/NotificationContext';
import { createExam, createExamWithAssignment, updateExam, fetchExamById } from '../../store/slices/ExamSlice';
import { fetchSubjects } from '../../store/slices/SubjectSlice';
import { fetchChaptersBySubject } from '../../store/slices/ChapterSlice';
import { fetchTopicsByChapter } from '../../store/slices/TopicSlice';
import { fetchSubtopicsByTopic } from '../../store/slices/SubtopicSlice';
import { fetchClasses } from '../../store/slices/ClassesSlice';
import { fetchAllOwnClasses } from '../../store/slices/ClassroomSlice';
import { aiGenerateQuestions } from '../../store/slices/QuestionSlice';
import { PageContainer, Stack, Card } from '../ui/layout';
import StepIndicator from './StepIndicator';
import ExamDetailsForm from './ExamDetailsForm';
import QuestionForm from './QuestionForm';
import QuestionList from './QuestionList';
import StudentAssignmentSelector from './StudentAssignmentSelector';
import { FiSave, FiArrowRight, FiArrowLeft, FiCheck } from 'react-icons/fi';

const getStepsForUserType = (userType) => {
  if (userType === 'teacher') {
    // Teachers need student assignment step
    return [
      { id: 1, title: 'Exam Details', description: 'Basic exam information' },
      { id: 2, title: 'Questions', description: 'Add exam questions' },
      { id: 3, title: 'Assignment', description: 'Assign to students' },
      { id: 4, title: 'Review', description: 'Review and publish' }
    ];
  }

  // Institutes create exams directly without assignment
  return [
    { id: 1, title: 'Exam Details', description: 'Basic exam information' },
    { id: 2, title: 'Questions', description: 'Add exam questions' },
    { id: 3, title: 'Review', description: 'Review and create exam' }
  ];
};

const ExamCreationWizard = ({
  examId,
  isEditing = false,
  onSuccess = null,
  userType = 'teacher'
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { currentTheme } = useThemeProvider();
  const { showWarning, showSuccess } = useNotification();

  // Get steps based on user type
  const STEPS = useMemo(() => getStepsForUserType(userType), [userType]);
  const maxSteps = STEPS.length;

  // Theme classes
  const themeClasses = useMemo(() => ({
    bg: currentTheme === "dark" ? "bg-gray-900" : "bg-white",
    text: currentTheme === "dark" ? "text-gray-100" : "text-gray-900",
    input: currentTheme === "dark" ? "bg-gray-800 text-gray-100 border-gray-700" : "bg-gray-50 text-gray-900 border-gray-300",
    label: currentTheme === "dark" ? "text-gray-300" : "text-gray-700",
    cardBg: currentTheme === "dark" ? "bg-gray-800" : "bg-white",
    button: currentTheme === "dark" ? "bg-blue-600 hover:bg-blue-700" : "bg-blue-600 hover:bg-blue-700"
  }), [currentTheme]);

  const [currentStep, setCurrentStep] = useState(1);
  const [examData, setExamData] = useState({
    title: '',
    description: '',
    total_marks: 0,
    total_duration: 0,
    start_time: '',
    question_ids: [],
    classNumber: '',
    subjectId: ''
  });
  const [questions, setQuestions] = useState([]);
  const [selectedStudents, setSelectedStudents] = useState([]);
  const [assignmentType, setAssignmentType] = useState('classroom');

  // Hierarchical data state
  const [subjectId, setSubjectId] = useState('');
  const [chapterId, setChapterId] = useState('');
  const [topicId, setTopicId] = useState('');
  const [subtopicId, setSubtopicId] = useState('');

  // AI Generation state
  const [aiNoOfQuestions, setAiNoOfQuestions] = useState(5);
  const [aiDifficultyMode, setAiDifficultyMode] = useState('balanced');
  const [aiNoOfEasy, setAiNoOfEasy] = useState(2);
  const [aiNoOfMedium, setAiNoOfMedium] = useState(2);
  const [aiNoOfHard, setAiNoOfHard] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Manual question form state
  const [questionType, setQuestionType] = useState('MCQS');
  const [questionForm, setQuestionForm] = useState({
    text: '',
    options: [
      { option_text: '', is_correct: false },
      { option_text: '', is_correct: false },
      { option_text: '', is_correct: false },
      { option_text: '', is_correct: false }
    ],
    answer: '',
    marks: 1,
    Level: 'EASY'
  });

  // Redux selectors
  const { classes = [], loading: classesLoading } = useSelector(state => state.classes);
  const { subjects = [], loading: subjectsLoading } = useSelector(state => state.subjects);
  const { chaptersBySubject = [], loading: chaptersLoading } = useSelector(state => state.chapters);
  const { topicsByChapter = [], loading: topicsLoading } = useSelector(state => state.topics);
  const { subtopicsByTopic = [], loading: subtopicsLoading } = useSelector(state => state.subtopics);
  const { aiGeneratedQuestions, loading: aiLoading, error: aiError } = useSelector(state => state.questions);
  const { classrooms = [], loading: classroomsLoading } = useSelector(state => state.classroom);

  // Handle classroom selection for assignment
  const handleClassroomIdChange = useCallback((classroomId) => {
    setExamData(prev => ({
      ...prev,
      classroom_id: classroomId
    }));
  }, []);

  const handleExamDataChange = useCallback((eventOrData) => {
    if (eventOrData.target) {
      // Handle form event
      const { name, value, type, checked } = eventOrData.target;
      const fieldValue = type === 'checkbox' ? checked : value;
      setExamData(prev => ({ ...prev, [name]: fieldValue }));
    } else {
      // Handle data object
      setExamData(prev => ({ ...prev, ...eventOrData }));
    }
  }, []);

  const handleQuestionsChange = useCallback((newQuestions) => {
    setQuestions(newQuestions);
    const totalMarks = newQuestions.reduce((sum, q) => sum + (q.marks || 0), 0);
    setExamData(prev => ({ ...prev, total_marks: totalMarks }));
  }, []);

  // Handle class number change
  const handleClassNumberChange = useCallback((e) => {
    const classNumber = e.target.value;
    setExamData(prev => ({ ...prev, classNumber }));
  }, []);

  // Handle subject change
  const handleSubjectChange = useCallback((e) => {
    const subjectId = e.target.value;
    setExamData(prev => ({ ...prev, subjectId }));
    setSubjectId(subjectId); // Also update the hierarchical state for questions
  }, []);

  // AI Generation handler
  const handleAIGenerate = useCallback(async () => {
    if (!subjectId || !chapterId) {
      console.log('Please select a subject and chapter first');
      return;
    }

    setIsSubmitting(true);
    try {
      // Find the selected subject and chapter names
      const selectedSubject = subjects.find(s => s.id === subjectId);
      const selectedChapter = chaptersBySubject.find(c => c.id === chapterId);
      const selectedTopic = topicsByChapter.find(t => t.id === topicId);
      const selectedSubtopic = subtopicsByTopic.find(st => st.id === subtopicId);

      // Prepare difficulty payload
      let difficultyPayload = {};
      if (aiDifficultyMode === "custom") {
        difficultyPayload = {
          no_of_easy: aiNoOfEasy,
          no_of_medium: aiNoOfMedium,
          no_of_hard: aiNoOfHard
        };
      }

      const payload = {
        class: examData.classNumber || "10", // Use class from exam details
        subject: selectedSubject?.name || "",
        chapter: selectedChapter?.name || "",
        no_of_questions: aiNoOfQuestions,
        topic: selectedTopic?.name || "",
        subtopic: selectedSubtopic?.name || "",
        ...difficultyPayload
      };

      console.log('AI Generation Payload:', payload);

      const result = await dispatch(aiGenerateQuestions(payload)).unwrap();

      if (result && result.questions && result.questions.length > 0) {
        // Add generated questions to the exam
        const newQuestions = result.questions.map((question, index) => ({
          ...question,
          id: Date.now() + index,
          class_number: examData.classNumber,
          subject_id: subjectId,
          chapter_id: chapterId,
          topic_id: topicId || null,
          subtopic_id: subtopicId || null,
        }));

        setQuestions(prev => [...prev, ...newQuestions]);
        console.log(`Generated ${result.questions.length} questions successfully!`);
      } else {
        console.log('No questions were generated. Please try again.');
      }
    } catch (error) {
      console.error('AI Generation Error:', error);
      console.error(error || 'Failed to generate questions. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [dispatch, examData.classNumber, subjectId, chapterId, topicId, subtopicId, subjects, chaptersBySubject, topicsByChapter, subtopicsByTopic, aiNoOfQuestions, aiDifficultyMode, aiNoOfEasy, aiNoOfMedium, aiNoOfHard]);

  // Manual question handlers
  const handleQuestionChange = useCallback((e) => {
    const { name, value } = e.target;
    setQuestionForm(prev => ({ ...prev, [name]: value }));
  }, []);

  const handleOptionChange = useCallback((index, field, value) => {
    setQuestionForm(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => {
        if (i === index) {
          if (field === 'is_correct') {
            // If setting this option as correct, unset all others
            return { ...opt, [field]: value };
          } else {
            return { ...opt, [field]: value };
          }
        } else if (field === 'is_correct' && value === true) {
          // If setting another option as correct, unset this one
          return { ...opt, is_correct: false };
        }
        return opt;
      })
    }));
  }, []);

  const handleAddQuestion = useCallback((e) => {
    e.preventDefault();

    if (!questionForm.text.trim()) {
      console.log('Please enter question text');
      return;
    }

    if (questionType === 'MCQS') {
      const filledOptions = questionForm.options.filter(opt => opt.option_text.trim() !== '');
      if (filledOptions.length < 2) {
        console.log('Please provide at least 2 options for multiple choice questions');
        return;
      }
      const hasCorrectAnswer = questionForm.options.some(opt => opt.is_correct && opt.option_text.trim() !== '');
      if (!hasCorrectAnswer) {
        console.log('Please specify the correct answer');
        return;
      }
    }

    const newQuestion = {
      id: Date.now(),
      text: questionForm.text,
      type: questionType,
      marks: parseInt(questionForm.marks) || 1,
      class_number: examData.classNumber,
      subject_id: subjectId,
      chapter_id: chapterId,
      topic_id: topicId || null,
      subtopic_id: subtopicId || null,
      ...(questionType === 'MCQS' && {
        options: questionForm.options.filter(opt => opt.option_text.trim() !== ''),
        correct_answer: questionForm.options.find(opt => opt.is_correct)?.option_text || ''
      }),
      ...(questionType === 'DESCRIPTIVE' && {
        answer: questionForm.answer
      }),
      Level: questionForm.Level
    };

    setQuestions(prev => [...prev, newQuestion]);

    // Reset form
    setQuestionForm({
      text: '',
      options: [
        { option_text: '', is_correct: false },
        { option_text: '', is_correct: false },
        { option_text: '', is_correct: false },
        { option_text: '', is_correct: false }
      ],
      answer: '',
      marks: 1,
      Level: 'EASY'
    });

    console.log('Question added successfully!');
  }, [questionForm, questionType, examData.classNumber, subjectId, chapterId, topicId, subtopicId]);

  // Data fetching effects
  useEffect(() => {
    // Fetch classes, subjects, and classrooms on mount
    dispatch(fetchClasses({ skip: 0, limit: 100 }));
    dispatch(fetchSubjects({ skip: 0, limit: 100 }));
    if (userType === 'teacher') {
      dispatch(fetchAllOwnClasses());
    }
  }, [dispatch, userType]);

  // Load existing exam data when editing
  useEffect(() => {
    if (isEditing && examId) {
      console.log('🔄 Loading existing exam data for editing:', examId);
      dispatch(fetchExamById(examId))
        .unwrap()
        .then((examData) => {
          console.log('✅ Loaded exam data:', examData);

          // Set exam details
          setExamData({
            title: examData.title || '',
            description: examData.description || '',
            total_marks: examData.total_marks || 0,
            total_duration: examData.total_duration || 0,
            start_time: examData.start_time || '',
            end_time: examData.end_time || '',
            class_number: examData.class_number || '',
            classNumber: examData.class_number || ''
          });

          // Set questions if they exist
          if (examData.questions && examData.questions.length > 0) {
            setQuestions(examData.questions);
            console.log('✅ Loaded questions:', examData.questions.length);
          }

          // Set subject if available from first question
          if (examData.questions && examData.questions.length > 0 && examData.questions[0].subject_id) {
            setSubjectId(examData.questions[0].subject_id);
          }
        })
        .catch((error) => {
          console.error('❌ Failed to load exam data:', error);
        });
    }
  }, [dispatch, isEditing, examId]);

  useEffect(() => {
    // Fetch chapters when subject changes
    if (subjectId) {
      dispatch(fetchChaptersBySubject({ subjectId, skip: 0, limit: 100 }));
      setChapterId(''); // Reset chapter selection
      setTopicId(''); // Reset topic selection
      setSubtopicId(''); // Reset subtopic selection
    }
  }, [dispatch, subjectId]);

  useEffect(() => {
    // Fetch topics when chapter changes
    if (chapterId) {
      dispatch(fetchTopicsByChapter({ chapterId, skip: 0, limit: 100 }));
      setTopicId(''); // Reset topic selection
      setSubtopicId(''); // Reset subtopic selection
    }
  }, [dispatch, chapterId]);

  useEffect(() => {
    // Fetch subtopics when topic changes
    if (topicId) {
      dispatch(fetchSubtopicsByTopic({ topicId, skip: 0, limit: 100 }));
      setSubtopicId(''); // Reset subtopic selection
    }
  }, [dispatch, topicId]);

  // Auto-calculate total marks and duration when questions change
  useEffect(() => {
    const totalMarks = questions.reduce((sum, question) => sum + (question.marks || 1), 0);

    // For institutes, use manual duration if set, otherwise calculate estimated duration (2 minutes per question)
    // For teachers, keep their manually set duration
    let calculatedDuration = examData.total_duration;
    if (userType === 'institute') {
      // If no manual duration is set (empty string or 0), auto-calculate
      if (!examData.total_duration || examData.total_duration === '' || examData.total_duration === 0) {
        calculatedDuration = questions.length * 2;
      }
    }

    setExamData(prev => ({
      ...prev,
      total_marks: totalMarks,
      ...(userType === 'institute' && { total_duration: calculatedDuration })
    }));
  }, [questions, userType, examData.total_duration]);

  // Validation logic
  const examDetailsValid = useMemo(() => {
    if (userType === 'institute') {
      // For institutes, title, description, class, and subject are required
      return examData.title && examData.title.trim() !== '' &&
             examData.description && examData.description.trim() !== '' &&
             examData.classNumber && examData.classNumber.trim() !== '' &&
             examData.subjectId && examData.subjectId.trim() !== '';
    } else {
      // For teachers, additional fields are required
      return examData.title && examData.title.trim() !== '' &&
             examData.description && examData.description.trim() !== '' &&
             examData.total_duration && examData.total_duration > 0 &&
             examData.start_time && examData.start_time.trim() !== '';
    }
  }, [examData, userType]);

  const handleNext = () => {
    // Validate current step before proceeding
    if (currentStep === 1) {
      // Validate exam details before moving to questions
      if (!examDetailsValid) {
        showWarning('Please fill in all required exam details before proceeding to questions.');
        return;
      }
    }

    if (currentStep === 2) {
      // Validate questions before moving to assignment (for teachers) or review
      if (questions.length === 0) {
        showWarning('Please add at least one question before proceeding.');
        return;
      }
    }

    if (currentStep === 3 && userType === 'teacher') {
      // Validate assignment before moving to review
      if (!assignmentType) {
        showWarning('Please select an assignment type (classroom or individual students).');
        return;
      }
      if (assignmentType === 'classroom' && !examData.classroom_id) {
        showWarning('Please select a classroom to assign the exam to.');
        return;
      }
      if (assignmentType === 'students' && selectedStudents.length === 0) {
        showWarning('Please select at least one student to assign the exam to.');
        return;
      }
    }

    if (currentStep < maxSteps) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSave = async () => {
    try {
      if (isEditing && userType === 'teacher') {
        // Only teachers can actually update existing exams
        // Build assignment object with only the required field
        const assignment = {};
        if (assignmentType === 'classroom') {
          assignment.classroom_id = examData.classroom_id;
        } else if (assignmentType === 'students') {
          assignment.student_ids = selectedStudents;
        }

        const examPayload = {
          ...examData,
          question_ids: questions.map(q => q.id),
          assignment
        };
        await dispatch(updateExam({ id: examId, examData: examPayload })).unwrap();
      } else {
        // For institutes: Always create new exam (even when "editing" - it's actually copying)
        // For teachers: Create new exam
        if (userType === 'institute') {
          // For institutes: Use direct exam creation API with questions included
          const examPayload = {
            title: examData.title,
            description: examData.description,
            total_marks: examData.total_marks,
            total_duration: examData.total_duration || questions.length * 2, // Auto-calculate if not set
            start_time: new Date().toISOString(), // Current time as default
            questions: questions.map(q => ({
              text: q.text,
              answer: q.answer,
              Type: q.Type,
              Level: q.Level,
              imageUrl: q.imageUrl || "",
              class_id: q.class_id,
              subject_id: q.subject_id,
              chapter_id: q.chapter_id,
              topic_id: q.topic_id || null,
              subtopic_id: q.subtopic_id || null,
              marks: q.marks || 1,
              options: q.options || []
            }))
          };
          await dispatch(createExam(examPayload)).unwrap();
        } else {
          // For teachers: Use create with assignment API
          // Build assignment object with only the required field
          const assignment = {};
          if (assignmentType === 'classroom') {
            assignment.classroom_id = examData.classroom_id;
          } else if (assignmentType === 'students') {
            assignment.student_ids = selectedStudents;
          }

          const examPayload = {
            ...examData,
            questions: questions.map(q => ({
              text: q.text,
              answer: q.answer,
              Type: q.Type,
              Level: q.Level,
              imageUrl: q.imageUrl || "",
              class_id: q.class_id || null,
              subject_id: q.subject_id || null,
              chapter_id: q.chapter_id || null,
              topic_id: q.topic_id || null,
              subtopic_id: q.subtopic_id || null,
              marks: q.marks || 1,
              options: q.options || []
            })),
            assignment
          };

          console.log('🚀 Creating exam with assignment payload:', JSON.stringify(examPayload, null, 2));
          const result = await dispatch(createExamWithAssignment(examPayload)).unwrap();
          console.log('✅ Exam created successfully with response:', result);

          // Show success message with assignment details
          if (result.exam_id && result.assigned_student_ids) {
            showSuccess(`Exam created successfully! Assigned to ${result.assigned_student_ids.length} student(s).`);
          } else {
            showSuccess('Exam created and assigned successfully!');
          }
        }
      }

      // Use custom success handler or default navigation
      if (onSuccess) {
        onSuccess();
      } else {
        // Default navigation based on user type
        const defaultPath = userType === 'institute' ? '/institute/exams' : '/teacher/exams';
        navigate(defaultPath);
      }
    } catch (error) {
      console.error('Failed to save exam:', error);
    }
  };

  const renderStepContent = () => {
    const currentStepData = STEPS[currentStep - 1];

    switch (currentStepData.title) {
      case 'Exam Details':
        return (
          <ExamDetailsForm
            exam={examData}
            onExamChange={handleExamDataChange}
            themeClasses={themeClasses}
            classrooms={[]}
            subjects={subjects}
            classes={classes}
            classId=""
            subjectId={examData.subjectId}
            classNumber={examData.classNumber}
            assignmentType="individual"
            onClassChange={() => {}}
            onSubjectChange={handleSubjectChange}
            onClassNumberChange={handleClassNumberChange}
            examDetailsValid={examDetailsValid}
            userType={userType}
          />
        );
      case 'Questions':
        return (
          <Stack gap="lg">
            <QuestionForm
              onQuestionAdd={handleAddQuestion}
              themeClasses={themeClasses}
              userType={userType}
              questionType={questionType}
              setQuestionType={setQuestionType}
              descType="text"
              setDescType={() => {}}
              questionForm={questionForm}
              onQuestionChange={handleQuestionChange}
              onOptionChange={handleOptionChange}
              onAIGenerate={handleAIGenerate}
              subjects={subjects}
              subjectId={subjectId}
              setSubjectId={setSubjectId}
              chaptersBySubject={chaptersBySubject}
              topicsByChapter={topicsByChapter}
              subtopicsByTopic={subtopicsByTopic}
              chapterId={chapterId}
              topicId={topicId}
              subtopicId={subtopicId}
              setChapterId={setChapterId}
              setTopicId={setTopicId}
              setSubtopicId={setSubtopicId}
              chaptersLoading={chaptersLoading}
              topicsLoading={topicsLoading}
              subtopicsLoading={subtopicsLoading}
              isSubmitting={isSubmitting}
              aiNoOfQuestions={aiNoOfQuestions}
              setAiNoOfQuestions={setAiNoOfQuestions}
              aiDifficultyMode={aiDifficultyMode}
              setAiDifficultyMode={setAiDifficultyMode}
              aiNoOfEasy={aiNoOfEasy}
              setAiNoOfEasy={setAiNoOfEasy}
              aiNoOfMedium={aiNoOfMedium}
              setAiNoOfMedium={setAiNoOfMedium}
              aiNoOfHard={aiNoOfHard}
              setAiNoOfHard={setAiNoOfHard}
              gradeClasses={classes}
            />
            <QuestionList
              questions={questions}
              onQuestionsChange={handleQuestionsChange}
              onEditQuestion={() => {}} // Add missing prop
              onDeleteQuestion={(index) => {
                setQuestions(prev => prev.filter((_, i) => i !== index));
              }}
              themeClasses={themeClasses}
            />
          </Stack>
        );
      case 'Assignment':
        return (
          <StudentAssignmentSelector
            assignmentType={assignmentType}
            onAssignmentTypeChange={setAssignmentType}
            selectedStudentIds={selectedStudents}
            onSelectedStudentsChange={setSelectedStudents}
            classId={examData.classroom_id}
            onClassIdChange={handleClassroomIdChange}
            classrooms={classrooms}
            themeClasses={themeClasses}
          />
        );
      case 'Review':
        return (
          <Card>
            <h3 className="text-lg font-semibold mb-4">Review Exam</h3>
            <Stack gap="md">
              <div>
                <strong>Title:</strong> {examData.title}
              </div>
              <div>
                <strong>Duration:</strong> {examData.total_duration} minutes
              </div>
              <div>
                <strong>Total Marks:</strong> {examData.total_marks}
              </div>
              <div>
                <strong>Questions:</strong> {questions.length}
              </div>
              {userType === 'teacher' && (
                <div>
                  <strong>Assignment:</strong> {
                    assignmentType === 'classroom'
                      ? `Entire Class${examData.classroom_id ? ` (${classrooms.find(c => c.id === examData.classroom_id)?.name || 'Selected Classroom'})` : ' (No classroom selected)'}`
                      : `${selectedStudents.length} Selected Students`
                  }
                </div>
              )}
              {userType === 'institute' && (
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                  <h4 className="text-blue-800 dark:text-blue-300 font-medium mb-2">🏆 Creating Reference Exam for Competitions</h4>
                  <p className="text-blue-700 dark:text-blue-400 text-sm">
                    This exam will be created as a reference template for competitions.
                    Students will access this exam through competition registrations, not direct assignments.
                  </p>
                </div>
              )}
            </Stack>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 py-8 space-y-6">
        {/* Enhanced Header Section */}
        <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-800 dark:via-gray-850 dark:to-gray-900 rounded-2xl p-6 border border-blue-100 dark:border-gray-700 shadow-lg">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600"></div>
            <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                  <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5"/>
                </pattern>
              </defs>
              <rect width="100" height="100" fill="url(#grid)" />
            </svg>
          </div>

          <div className="relative flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <FiCheck className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1">
              <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent mb-1">
                {isEditing
                  ? (userType === 'institute' ? 'Copy & Create Question Bank' : 'Edit Exam')
                  : (userType === 'institute' ? 'Create Question Bank' : 'Create New Exam')
                }
              </h1>
              <p className="text-gray-600 dark:text-gray-400 text-sm md:text-base leading-relaxed mb-3">
                {userType === 'institute'
                  ? 'Build a comprehensive question bank for competitions with AI-powered question generation'
                  : 'Create engaging exams with our step-by-step wizard and AI assistance'
                }
              </p>
              <div className="flex flex-wrap items-center gap-3">
                <div className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400">
                  <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
                  Step {currentStep} of {maxSteps}
                </div>
                <div className="flex items-center gap-2 text-xs text-green-600 dark:text-green-400">
                  <FiCheck className="w-3 h-3" />
                  {questions.length} Questions Added
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Step Indicator */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">Progress Overview</h2>
            <p className="text-gray-600 dark:text-gray-400 text-sm">Track your exam creation progress</p>
          </div>
          <StepIndicator
            steps={STEPS}
            currentStep={currentStep}
            examDetailsValid={examDetailsValid}
            questionsCount={questions.length}
          />
        </div>

        {/* Enhanced Main Content Area */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 min-h-96">
          <div className="p-6">
            {renderStepContent()}
          </div>
        </div>

        {/* Enhanced Navigation Section */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-4">
            {/* Previous Button */}
            <button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="w-full lg:w-auto flex items-center justify-center px-6 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 group"
            >
              <FiArrowLeft className="mr-2 w-4 h-4 group-hover:-translate-x-1 transition-transform duration-200" />
              <span className="font-medium">Previous</span>
            </button>

            {/* Progress Indicator */}
            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 dark:text-blue-400 font-semibold text-xs">{currentStep}</span>
                </div>
                <span className="text-xs">of</span>
                <div className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 dark:text-gray-400 font-semibold text-xs">{maxSteps}</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
              <button
                onClick={handleSave}
                className="w-full sm:w-auto flex items-center justify-center px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-gray-500 group"
              >
                <FiSave className="mr-2 w-4 h-4 group-hover:rotate-12 transition-transform duration-200" />
                <span className="font-medium">Save Draft</span>
              </button>

              {currentStep < maxSteps ? (
                <div className="relative group w-full sm:w-auto">
                  <button
                    onClick={handleNext}
                    disabled={currentStep === 1 && !examDetailsValid}
                    className={`w-full flex items-center justify-center px-6 py-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-medium ${
                      currentStep === 1 && !examDetailsValid
                        ? 'bg-gray-400 cursor-not-allowed text-gray-200 shadow-none transform-none'
                        : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white border border-blue-500'
                    }`}
                    title={currentStep === 1 && !examDetailsValid ? 'Please complete all required exam details first' : ''}
                  >
                    <span>Next Step</span>
                    <FiArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
                  </button>
                  {currentStep === 1 && !examDetailsValid && (
                    <div className="absolute -top-12 right-0 bg-gray-900 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap shadow-lg">
                      Complete exam details first
                      <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                    </div>
                  )}
                </div>
              ) : (
                <button
                  onClick={handleSave}
                  className="w-full sm:w-auto flex items-center justify-center px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-green-500 group"
                >
                  <FiCheck className="mr-2 w-4 h-4 group-hover:scale-110 transition-transform duration-200" />
                  <span className="font-medium">
                    {userType === 'institute' ? 'Create Question Bank' : 'Publish Exam'}
                  </span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExamCreationWizard;
