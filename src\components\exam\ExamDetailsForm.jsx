import React from 'react';

const ExamDetailsForm = ({
  exam = {},
  onExamChange,
  subjects = [],
  classes = [],
  subjectId = '',
  classNumber = '',
  onSubjectChange,
  onClassNumberChange
}) => {

  return (
    <div className="space-y-6">
      {/* Exam Information */}
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-lg font-semibold mb-4">Exam Information</h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="title"
              value={exam.title || ''}
              onChange={onExamChange}
              className="w-full p-3 border rounded-lg"
              placeholder="Enter exam title"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Description</label>
            <textarea
              name="description"
              value={exam.description || ''}
              onChange={onExamChange}
              rows={3}
              className="w-full p-3 border rounded-lg"
              placeholder="Enter exam description"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Start Time <span className="text-red-500">*</span>
              </label>
              <input
                type="datetime-local"
                name="start_time"
                value={exam.start_time || ''}
                onChange={onExamChange}
                className="w-full p-3 border rounded-lg"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Duration (minutes) <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="total_duration"
                value={exam.total_duration || 60}
                onChange={onExamChange}
                min="1"
                className="w-full p-3 border rounded-lg"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Subject and Grade */}
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-lg font-semibold mb-4">Subject & Grade</h3>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Subject <span className="text-red-500">*</span>
            </label>
            <select
              value={subjectId}
              onChange={onSubjectChange}
              className="w-full p-3 border rounded-lg"
            >
              <option value="">Select Subject</option>
              {subjects.map(subject => (
                <option key={subject.id} value={subject.id}>{subject.name}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Grade <span className="text-red-500">*</span>
            </label>
            <select
              value={classNumber}
              onChange={onClassNumberChange}
              className="w-full p-3 border rounded-lg"
            >
              <option value="">Select Grade</option>
              {classes.map(cls => (
                <option key={cls.id} value={cls.ClassNo}>Grade {cls.ClassNo}</option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExamDetailsForm;
