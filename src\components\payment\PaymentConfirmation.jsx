/**
 * PaymentConfirmation Component
 * 
 * Displays payment confirmation details and registration information
 * after successful payment completion.
 */

import React from 'react';
import {
  FiCheck,
  FiCalendar,
  FiMapPin,
  FiMail,
  FiDownload,
  FiShare2,
  FiCreditCard,
  FiGrid,
  FiUser
} from 'react-icons/fi';

const PaymentConfirmation = ({
  payment,
  registration,
  event,
  onDownloadReceipt,
  onShareConfirmation,
  className = ''
}) => {
  // Format currency
  const formatCurrency = (amount, currency = 'ZAR') => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format time
  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('en-ZA', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg overflow-hidden ${className}`}>
      {/* Success Header */}
      <div className="bg-green-50 border-b border-green-200 p-6">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-green-100 rounded-full">
            <FiCheck className="w-8 h-8 text-green-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-green-800">
              Payment Successful!
            </h2>
            <p className="text-green-600 mt-1">
              Your registration has been confirmed
            </p>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Payment Details */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <FiCreditCard className="w-5 h-5" />
            <span>Payment Details</span>
          </h3>
          
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Payment ID:</span>
              <span className="font-mono text-sm">
                {payment?.payment_id?.slice(-12) || 'N/A'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Amount Paid:</span>
              <span className="font-semibold text-lg">
                {formatCurrency(payment?.amount, payment?.currency)}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Payment Date:</span>
              <span>
                {payment?.processed_at ? formatDate(payment.processed_at) : 'Just now'}
              </span>
            </div>
            
            {payment?.demo_payment_id && (
              <div className="flex justify-between">
                <span className="text-gray-600">Demo Reference:</span>
                <span className="font-mono text-sm">
                  {payment.demo_payment_id}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Registration Details */}
        {registration && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
              <FiUser className="w-5 h-5" />
              <span>Registration Details</span>
            </h3>
            
            <div className="bg-blue-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Confirmation Number:</span>
                <span className="font-bold text-blue-600">
                  {registration.confirmation_number}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Registration Date:</span>
                <span>
                  {formatDate(registration.registration_date)}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Confirmed
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Event Details */}
        {event && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
              <FiCalendar className="w-5 h-5" />
              <span>Event Details</span>
            </h3>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start space-x-4">
                {event.image_url && (
                  <img
                    src={event.image_url}
                    alt={event.title}
                    className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
                  />
                )}
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-2">
                    {event.title}
                  </h4>
                  
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <FiCalendar className="w-4 h-4" />
                      <span>
                        {formatDate(event.start_date)} at {formatTime(event.start_date)}
                      </span>
                    </div>
                    
                    {event.location && (
                      <div className="flex items-center space-x-2">
                        <FiMapPin className="w-4 h-4" />
                        <span>{event.location}</span>
                      </div>
                    )}
                    
                    {event.venue_address && (
                      <div className="text-gray-500 ml-6">
                        {event.venue_address}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* QR Code */}
        {registration?.qr_code && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
              <FiGrid className="w-5 h-5" />
              <span>Event Check-in</span>
            </h3>
            
            <div className="bg-gray-50 rounded-lg p-4 text-center">
              <div className="inline-block p-4 bg-white rounded-lg shadow-sm">
                <img
                  src={`data:image/png;base64,${registration.qr_code}`}
                  alt="Event QR Code"
                  className="w-32 h-32 mx-auto"
                />
              </div>
              <p className="text-sm text-gray-600 mt-3">
                Show this QR code at the event for quick check-in
              </p>
            </div>
          </div>
        )}

        {/* Important Information */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-semibold text-yellow-800 mb-2">
            Important Information
          </h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Please arrive 15 minutes before the event starts</li>
            <li>• Bring a valid ID for verification</li>
            <li>• Save this confirmation for your records</li>
            <li>• Contact support if you need to make changes</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          {onDownloadReceipt && (
            <button
              onClick={onDownloadReceipt}
              className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
            >
              <FiDownload className="w-4 h-4" />
              <span>Download Receipt</span>
            </button>
          )}
          
          {onShareConfirmation && (
            <button
              onClick={onShareConfirmation}
              className="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2"
            >
              <FiShare2 className="w-4 h-4" />
              <span>Share</span>
            </button>
          )}
          
          <button
            onClick={() => window.print()}
            className="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2"
          >
            <FiDownload className="w-4 h-4" />
            <span>Print</span>
          </button>
        </div>

        {/* Email Confirmation Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start space-x-2">
            <FiMail className="w-5 h-5 text-blue-500 mt-0.5" />
            <div className="text-sm text-blue-700">
              <p className="font-medium">Email Confirmation Sent</p>
              <p>
                A confirmation email with all details has been sent to your registered email address.
                Please check your spam folder if you don't see it in your inbox.
              </p>
            </div>
          </div>
        </div>

        {/* Support Contact */}
        <div className="text-center text-sm text-gray-500 pt-4 border-t border-gray-200">
          <p>
            Need help? Contact our support team at{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentConfirmation;
