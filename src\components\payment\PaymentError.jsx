/**
 * PaymentError Component
 * 
 * Displays payment error messages and provides recovery options
 * for failed PayFast payments.
 */

import React from 'react';
import {
  FiAlertCircle,
  FiRefreshCw,
  FiArrowLeft,
  FiMail,
  FiPhone,
  FiCreditCard,
  FiInfo
} from 'react-icons/fi';

const PaymentError = ({
  error,
  payment,
  onRetry,
  onGoBack,
  onContactSupport,
  showRetryButton = true,
  showBackButton = true,
  className = ''
}) => {
  // Get error type and details
  const getErrorInfo = (errorMessage) => {
    const message = errorMessage?.toLowerCase() || '';
    
    if (message.includes('insufficient funds') || message.includes('declined')) {
      return {
        type: 'payment_declined',
        title: 'Payment Declined',
        description: 'Your payment was declined by your bank or card issuer.',
        suggestions: [
          'Check that you have sufficient funds in your account',
          'Verify your card details are correct',
          'Try using a different payment method',
          'Contact your bank if the issue persists'
        ]
      };
    }
    
    if (message.includes('expired') || message.includes('timeout')) {
      return {
        type: 'payment_timeout',
        title: 'Payment Timeout',
        description: 'The payment session has expired or timed out.',
        suggestions: [
          'Try the payment process again',
          'Ensure you have a stable internet connection',
          'Complete the payment within the time limit'
        ]
      };
    }
    
    if (message.includes('cancelled') || message.includes('canceled')) {
      return {
        type: 'payment_cancelled',
        title: 'Payment Cancelled',
        description: 'The payment was cancelled before completion.',
        suggestions: [
          'You can try the payment again',
          'Make sure to complete all payment steps',
          'Contact support if you need assistance'
        ]
      };
    }
    
    if (message.includes('network') || message.includes('connection')) {
      return {
        type: 'network_error',
        title: 'Connection Error',
        description: 'There was a problem connecting to the payment gateway.',
        suggestions: [
          'Check your internet connection',
          'Try again in a few minutes',
          'Contact support if the problem continues'
        ]
      };
    }
    
    // Generic error
    return {
      type: 'generic_error',
      title: 'Payment Error',
      description: errorMessage || 'An unexpected error occurred during payment processing.',
      suggestions: [
        'Try the payment process again',
        'Check your payment details',
        'Contact support if the issue persists'
      ]
    };
  };

  const errorInfo = getErrorInfo(error);

  // Format currency
  const formatCurrency = (amount, currency = 'ZAR') => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg overflow-hidden ${className}`}>
      {/* Error Header */}
      <div className="bg-red-50 border-b border-red-200 p-6">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-red-100 rounded-full">
            <FiAlertCircle className="w-8 h-8 text-red-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-red-800">
              {errorInfo.title}
            </h2>
            <p className="text-red-600 mt-1">
              {errorInfo.description}
            </p>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Payment Details (if available) */}
        {payment && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
              <FiCreditCard className="w-5 h-5" />
              <span>Payment Details</span>
            </h3>
            
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              {payment.payment_id && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment ID:</span>
                  <span className="font-mono text-sm">
                    {payment.payment_id.slice(-12)}
                  </span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span className="text-gray-600">Amount:</span>
                <span className="font-semibold">
                  {formatCurrency(payment.amount, payment.currency)}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className="text-red-600 font-semibold capitalize">
                  {payment.status || 'Failed'}
                </span>
              </div>
              
              {payment.failure_reason && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Reason:</span>
                  <span className="text-red-600">
                    {payment.failure_reason}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Error Details */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h4 className="font-semibold text-red-800 mb-2">
            What happened?
          </h4>
          <p className="text-red-700 text-sm">
            {errorInfo.description}
          </p>
        </div>

        {/* Suggestions */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <FiInfo className="w-5 h-5" />
            <span>What you can do</span>
          </h3>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <ul className="space-y-2">
              {errorInfo.suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start space-x-2 text-blue-700">
                  <span className="text-blue-500 mt-1">•</span>
                  <span className="text-sm">{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Common Issues */}
        <div>
          <h4 className="font-semibold text-gray-900 mb-3">
            Common Issues & Solutions
          </h4>
          
          <div className="space-y-3">
            <div className="border border-gray-200 rounded-lg p-3">
              <h5 className="font-medium text-gray-900 mb-1">
                Card Declined
              </h5>
              <p className="text-sm text-gray-600">
                Check with your bank, verify card details, or try a different payment method.
              </p>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-3">
              <h5 className="font-medium text-gray-900 mb-1">
                Session Timeout
              </h5>
              <p className="text-sm text-gray-600">
                Complete the payment process quickly and ensure stable internet connection.
              </p>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-3">
              <h5 className="font-medium text-gray-900 mb-1">
                Technical Issues
              </h5>
              <p className="text-sm text-gray-600">
                Try refreshing the page, clearing browser cache, or using a different browser.
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          {showRetryButton && onRetry && (
            <button
              onClick={onRetry}
              className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
            >
              <FiRefreshCw className="w-4 h-4" />
              <span>Try Again</span>
            </button>
          )}
          
          {showBackButton && onGoBack && (
            <button
              onClick={onGoBack}
              className="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2"
            >
              <FiArrowLeft className="w-4 h-4" />
              <span>Go Back</span>
            </button>
          )}
        </div>

        {/* Support Contact */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-900 mb-3">
            Need Help?
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <FiMail className="w-5 h-5 text-gray-500" />
              <div>
                <p className="text-sm font-medium text-gray-900">Email Support</p>
                <a 
                  href="mailto:<EMAIL>"
                  className="text-sm text-blue-600 hover:underline"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <FiPhone className="w-5 h-5 text-gray-500" />
              <div>
                <p className="text-sm font-medium text-gray-900">Phone Support</p>
                <a 
                  href="tel:+27123456789"
                  className="text-sm text-blue-600 hover:underline"
                >
                  +27 12 345 6789
                </a>
              </div>
            </div>
            
            {onContactSupport && (
              <button
                onClick={onContactSupport}
                className="w-full mt-3 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-sm"
              >
                Contact Support
              </button>
            )}
          </div>
        </div>

        {/* PayFast Information */}
        <div className="text-center text-xs text-gray-500 pt-4 border-t border-gray-200">
          <p>
            Payments are processed securely by PayFast, South Africa's leading payment gateway.
          </p>
          <p className="mt-1">
            For PayFast-specific issues, visit{' '}
            <a 
              href="/support"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              PayFast Help Center
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentError;
