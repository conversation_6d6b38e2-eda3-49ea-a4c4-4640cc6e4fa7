/**
 * PaymentStatus Component
 * 
 * Displays payment status and handles real-time status updates
 * for demo payments.
 */

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiClock,
  FiCheck,
  FiX,
  FiRefreshCw,
  FiAlertCircle,
  FiCreditCard,
  FiLoader
} from 'react-icons/fi';
import {
  getPaymentStatus,
  pollPaymentStatus,
  selectPaymentStatus,
  selectStatusLoading,
  selectStatusError,
  selectPollingLoading,
  selectPollingError
} from '../../store/slices/PaymentSlice';
import { LoadingSpinner } from '../ui';

const PaymentStatus = ({
  paymentId,
  onStatusChange,
  autoRefresh = true,
  refreshInterval = 5000,
  className = ''
}) => {
  const dispatch = useDispatch();
  const [lastRefresh, setLastRefresh] = useState(Date.now());

  // Redux selectors
  const paymentStatus = useSelector(selectPaymentStatus);
  const statusLoading = useSelector(selectStatusLoading);
  const statusError = useSelector(selectStatusError);
  const pollingLoading = useSelector(selectPollingLoading);
  const pollingError = useSelector(selectPollingError);

  // Load initial status
  useEffect(() => {
    if (paymentId) {
      dispatch(getPaymentStatus(paymentId));
    }
  }, [dispatch, paymentId]);

  // Auto-refresh for pending payments
  useEffect(() => {
    if (!autoRefresh || !paymentId || !paymentStatus) return;

    const isPending = paymentStatus.status === 'pending';
    if (!isPending) return;

    const interval = setInterval(() => {
      dispatch(getPaymentStatus(paymentId));
      setLastRefresh(Date.now());
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [dispatch, paymentId, paymentStatus, autoRefresh, refreshInterval]);

  // Handle status changes
  useEffect(() => {
    if (paymentStatus && onStatusChange) {
      onStatusChange(paymentStatus);
    }
  }, [paymentStatus, onStatusChange]);

  // Manual refresh
  const handleRefresh = () => {
    if (paymentId) {
      dispatch(getPaymentStatus(paymentId));
      setLastRefresh(Date.now());
    }
  };

  // Start polling
  const handleStartPolling = () => {
    if (paymentId) {
      dispatch(pollPaymentStatus({ paymentId }));
    }
  };

  // Get status display info
  const getStatusInfo = (status) => {
    switch (status) {
      case 'pending':
        return {
          icon: FiClock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          title: 'Payment Pending',
          description: 'Your payment is being processed. This may take a few minutes.'
        };
      case 'completed':
        return {
          icon: FiCheck,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          title: 'Payment Successful',
          description: 'Your payment has been processed successfully.'
        };
      case 'failed':
        return {
          icon: FiX,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          title: 'Payment Failed',
          description: 'Your payment could not be processed. Please try again.'
        };
      case 'refunded':
        return {
          icon: FiRefreshCw,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          title: 'Payment Refunded',
          description: 'Your payment has been refunded.'
        };
      default:
        return {
          icon: FiAlertCircle,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: 'Unknown Status',
          description: 'Payment status is unknown.'
        };
    }
  };

  // Format currency
  const formatCurrency = (amount, currency = 'ZAR') => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (statusLoading && !paymentStatus) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="flex items-center justify-center space-x-3">
          <LoadingSpinner size="sm" />
          <span className="text-gray-600">Loading payment status...</span>
        </div>
      </div>
    );
  }

  if (statusError && !paymentStatus) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="text-center">
          <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Error Loading Payment Status
          </h3>
          <p className="text-gray-600 mb-4">{statusError}</p>
          <button
            onClick={handleRefresh}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!paymentStatus) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="text-center text-gray-500">
          No payment status available
        </div>
      </div>
    );
  }

  const statusInfo = getStatusInfo(paymentStatus.status);
  const StatusIcon = statusInfo.icon;

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={`p-2 ${statusInfo.bgColor} rounded-lg`}>
            <FiCreditCard className={`w-6 h-6 ${statusInfo.color}`} />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Payment Status
            </h3>
            <p className="text-sm text-gray-500">
              Payment ID: {paymentStatus.payment_id?.slice(-8)}
            </p>
          </div>
        </div>
        
        {/* Refresh Button */}
        <button
          onClick={handleRefresh}
          disabled={statusLoading}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          title="Refresh status"
        >
          <FiRefreshCw className={`w-5 h-5 ${statusLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* Status Display */}
      <div className={`border ${statusInfo.borderColor} ${statusInfo.bgColor} rounded-lg p-4 mb-6`}>
        <div className="flex items-start space-x-3">
          <StatusIcon className={`w-6 h-6 ${statusInfo.color} mt-0.5`} />
          <div className="flex-1">
            <h4 className={`font-semibold ${statusInfo.color} mb-1`}>
              {statusInfo.title}
            </h4>
            <p className="text-gray-700 text-sm">
              {statusInfo.description}
            </p>
          </div>
        </div>
      </div>

      {/* Payment Details */}
      <div className="space-y-3 mb-6">
        <div className="flex justify-between">
          <span className="text-gray-600">Amount:</span>
          <span className="font-semibold">
            {formatCurrency(paymentStatus.amount, paymentStatus.currency)}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Status:</span>
          <span className={`font-semibold capitalize ${statusInfo.color}`}>
            {paymentStatus.status}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Created:</span>
          <span className="text-gray-900">
            {formatDate(paymentStatus.created_at)}
          </span>
        </div>
        
        {paymentStatus.processed_at && (
          <div className="flex justify-between">
            <span className="text-gray-600">Processed:</span>
            <span className="text-gray-900">
              {formatDate(paymentStatus.processed_at)}
            </span>
          </div>
        )}
        
        {paymentStatus.demo_payment_id && (
          <div className="flex justify-between">
            <span className="text-gray-600">Demo ID:</span>
            <span className="text-gray-900 font-mono text-sm">
              {paymentStatus.demo_payment_id}
            </span>
          </div>
        )}
      </div>

      {/* Gateway Response Details */}
      {paymentStatus.gateway_response && (
        <div className="border-t border-gray-200 pt-4">
          <h5 className="font-medium text-gray-900 mb-3">Payment Details</h5>
          <div className="space-y-2 text-sm">
            {paymentStatus.gateway_response.amount_gross && (
              <div className="flex justify-between">
                <span className="text-gray-600">Gross Amount:</span>
                <span>{formatCurrency(paymentStatus.gateway_response.amount_gross, paymentStatus.currency)}</span>
              </div>
            )}
            {paymentStatus.gateway_response.amount_fee && (
              <div className="flex justify-between">
                <span className="text-gray-600">Processing Fee:</span>
                <span>{formatCurrency(paymentStatus.gateway_response.amount_fee, paymentStatus.currency)}</span>
              </div>
            )}
            {paymentStatus.gateway_response.amount_net && (
              <div className="flex justify-between">
                <span className="text-gray-600">Net Amount:</span>
                <span>{formatCurrency(paymentStatus.gateway_response.amount_net, paymentStatus.currency)}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Actions for Pending Payments */}
      {paymentStatus.status === 'pending' && (
        <div className="border-t border-gray-200 pt-4">
          <button
            onClick={handleStartPolling}
            disabled={pollingLoading}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
          >
            {pollingLoading ? (
              <>
                <LoadingSpinner size="sm" />
                <span>Checking Status...</span>
              </>
            ) : (
              <>
                <FiRefreshCw className="w-4 h-4" />
                <span>Check Status Now</span>
              </>
            )}
          </button>
          
          {pollingError && (
            <p className="text-red-500 text-sm mt-2 text-center">
              {pollingError}
            </p>
          )}
        </div>
      )}

      {/* Last Refresh Time */}
      <div className="text-xs text-gray-500 text-center mt-4">
        Last updated: {new Date(lastRefresh).toLocaleTimeString()}
      </div>
    </div>
  );
};

export default PaymentStatus;
