/**
 * PaymentPage Component
 *
 * Main payment page that handles event registration payments
 * with demo purchase functionality (no real payment processing).
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiArrowLeft,
  FiShield,
  FiCreditCard,
  FiInfo
} from 'react-icons/fi';
import { DemoTicketPurchase } from '../../components/payments';
import { PaymentError } from '../../components/payment';
import { LoadingSpinner, ErrorMessage } from '../../components/ui';
import {
  fetchEventDetails,
  selectCurrentEvent,
  selectEventDetailsLoading,
  selectEventDetailsError
} from '../../store/slices/EventsSlice';
import {
  selectCurrentPayment,
  selectCreatePaymentError,
  clearPaymentErrors
} from '../../store/slices/PaymentSlice';

const PaymentPage = () => {
  const { eventId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  
  // Get query parameters
  const registrationId = searchParams.get('registration_id');
  const amount = searchParams.get('amount');
  const paymentType = searchParams.get('type') || 'event';
  
  // Local state
  const [paymentData, setPaymentData] = useState(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [error, setError] = useState(null);

  // Redux selectors
  const currentEvent = useSelector(selectCurrentEvent);
  const eventDetailsLoading = useSelector(selectEventDetailsLoading);
  const eventDetailsError = useSelector(selectEventDetailsError);
  const currentPayment = useSelector(selectCurrentPayment);
  const createPaymentError = useSelector(selectCreatePaymentError);

  // Load event details if needed
  useEffect(() => {
    if (eventId && paymentType === 'event') {
      dispatch(fetchEventDetails(eventId));
    }
  }, [dispatch, eventId, paymentType]);

  // Initialize payment data
  useEffect(() => {
    if (paymentType === 'event' && currentEvent && registrationId) {
      const userData = JSON.parse(localStorage.getItem('userdata') || '{}');
      
      setPaymentData({
        registration_id: registrationId,
        amount: parseFloat(amount) || currentEvent.registration_fee || 0,
        currency: currentEvent.currency || 'ZAR',
        user_email: userData.email || '',
        user_name: `${userData.first_name || ''} ${userData.last_name || ''}`.trim(),
        return_url: `${window.location.origin}/payment/success`,
        cancel_url: `${window.location.origin}/payment/cancel`
      });
      
      setShowPaymentForm(true);
    }
  }, [currentEvent, registrationId, amount, paymentType]);

  // Clear errors on mount
  useEffect(() => {
    dispatch(clearPaymentErrors());
    setError(null);
  }, [dispatch]);

  // Handle payment success
  const handlePaymentSuccess = (payment) => {
    console.log('Demo purchase completed successfully:', payment);
    // Demo purchase is instant, no redirect needed
  };

  // Handle payment error
  const handlePaymentError = (errorMessage) => {
    setError(errorMessage);
  };

  // Handle payment cancel
  const handlePaymentCancel = () => {
    navigate(-1); // Go back to previous page
  };

  // Handle retry payment
  const handleRetryPayment = () => {
    setError(null);
    dispatch(clearPaymentErrors());
  };

  // Handle go back
  const handleGoBack = () => {
    navigate(-1);
  };

  // Loading state
  if (eventDetailsLoading && paymentType === 'event') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-600 mt-4">Loading event details...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (eventDetailsError && paymentType === 'event') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <ErrorMessage 
            message={eventDetailsError}
            onRetry={() => dispatch(fetchEventDetails(eventId))}
          />
        </div>
      </div>
    );
  }

  // Missing required parameters
  if (paymentType === 'event' && (!registrationId || !amount)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
          <FiInfo className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Invalid Payment Request
          </h2>
          <p className="text-gray-600 mb-4">
            Missing required payment information. Please try registering for the event again.
          </p>
          <button
            onClick={handleGoBack}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleGoBack}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <FiArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {paymentType === 'event' ? 'Event Payment' : 'Subscription Payment'}
              </h1>
              <p className="text-sm text-gray-500">
                Demo purchase mode - no real payment required
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Payment Form */}
          <div className="lg:col-span-2">
            {error || createPaymentError ? (
              <PaymentError
                error={error || createPaymentError}
                payment={currentPayment}
                onRetry={handleRetryPayment}
                onGoBack={handleGoBack}
              />
            ) : showPaymentForm && paymentData ? (
              <DemoTicketPurchase
                eventId={eventId}
                ticketId={paymentData.ticket_id || 'demo-ticket'}
                ticketData={{
                  name: paymentData.item_name || 'Event Ticket',
                  price: paymentData.amount || 0,
                  quantity: 1,
                  total_amount: paymentData.amount || 0
                }}
                onSuccess={handlePaymentSuccess}
                onCancel={handlePaymentCancel}
                onError={handlePaymentError}
              />
            ) : (
              <div className="bg-white rounded-lg shadow-lg p-6 text-center">
                <LoadingSpinner size="lg" />
                <p className="text-gray-600 mt-4">Preparing payment...</p>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Event Summary (for event payments) */}
            {paymentType === 'event' && currentEvent && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Event Summary
                </h3>
                
                <div className="space-y-4">
                  {currentEvent.image_url && (
                    <img
                      src={currentEvent.image_url}
                      alt={currentEvent.title}
                      className="w-full h-32 object-cover rounded-lg"
                    />
                  )}
                  
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">
                      {currentEvent.title}
                    </h4>
                    <p className="text-sm text-gray-600 line-clamp-3">
                      {currentEvent.description}
                    </p>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Date:</span>
                      <span className="text-gray-900">
                        {new Date(currentEvent.start_date).toLocaleDateString()}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Location:</span>
                      <span className="text-gray-900">{currentEvent.location}</span>
                    </div>
                    
                    <div className="flex justify-between font-semibold">
                      <span className="text-gray-600">Amount:</span>
                      <span className="text-gray-900">
                        {currentEvent.currency} {paymentData?.amount?.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Security Information */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <FiShield className="w-5 h-5 text-green-600" />
                <span>Secure Payment</span>
              </h3>
              
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-start space-x-2">
                  <FiCreditCard className="w-4 h-4 text-gray-400 mt-0.5" />
                  <span>SSL encrypted payment processing</span>
                </div>
                
                <div className="flex items-start space-x-2">
                  <FiShield className="w-4 h-4 text-gray-400 mt-0.5" />
                  <span>PCI DSS compliant payment gateway</span>
                </div>
                
                <div className="flex items-start space-x-2">
                  <FiInfo className="w-4 h-4 text-gray-400 mt-0.5" />
                  <span>Your payment information is never stored</span>
                </div>
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-200">
                <p className="text-xs text-gray-500">
                  Demo Mode - No real payment processing
                </p>
              </div>
            </div>

            {/* Support Information */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">
                Need Help?
              </h4>
              <p className="text-sm text-blue-700 mb-3">
                If you experience any issues with your payment, our support team is here to help.
              </p>
              <div className="space-y-1 text-sm text-blue-600">
                <p>Email: <EMAIL></p>
                <p>Phone: +27 12 345 6789</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentPage;
