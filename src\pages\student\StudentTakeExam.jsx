import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import {
  setCurrentSessionId,
  selectExamSessionData,
  selectExamData
} from "../../store/slices/exam/examSessionDataSlice";
import {
  submitExamSession,
  setConnectionStatus,
  setSessionStatus,
  setSessionId,
  updateAnswer,
  setRemainingTime,
  decrementTime,
  addStrike
} from "../../store/slices/exam/examSessionSlice";
import { useNotification } from "../../contexts/NotificationContext";

import ExamSecurityService from "../../services/exam/security/ExamSecurityService";
import ExamSessionManager from "../../services/exam/session/ExamSessionManager";
import ConnectionMonitor from "../../components/exam/student/reconnection/ConnectionMonitor";

// Components
import ExamHeader from "../../components/exam/student/ExamHeader";
import ExamQuestion from "../../components/exam/student/ExamQuestion";
import ExamNavigation from "../../components/exam/student/ExamNavigation";
import ExamLoadingState from "../../components/exam/student/ExamLoadingState";
import ExamStartScreen from "../../components/exam/student/ExamStartScreen";

function StudentTakeExam() {
  const { examId: paramExamId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { showError, showSuccess } = useNotification();

  // State
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [examStarted, setExamStarted] = useState(false);

  // Redux selectors
  const examSessionData = useSelector(selectExamSessionData);
  const sessionExamData = useSelector(selectExamData);
  const examSession = useSelector((state) => state.examSession);

  // Extract exam ID from URL path (similar to other components in DashboardLayout)
  const pathParts = location.pathname.split('/');
  const examIdFromPath = pathParts[pathParts.length - 1];

  // Get exam ID from params, location state, or URL path
  const examId = paramExamId || location.state?.examId || examIdFromPath;
  const competitionMode = location.state?.competitionMode || false;
  const competitionEvent = location.state?.event;

  // Get current exam data
  const getCurrentExamData = () => {
    return sessionExamData || examSessionData.examData || null;
  };

  const currentExam = getCurrentExamData();
  const isLoading = examSessionData.loading || examSession.loading;
  const error = examSessionData.error || examSession.error;

  // Timer effect
  useEffect(() => {
    if (examSession.status === 'active' && examSession.remainingTimeSeconds > 0) {
      const timer = setInterval(() => {
        dispatch(decrementTime());
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [examSession.status, examSession.remainingTimeSeconds, dispatch]);

  // Auto-submit when time runs out
  useEffect(() => {
    if (examSession.remainingTimeSeconds <= 0 && examSession.status === 'active') {
      handleSubmitExam();
    }
  }, [examSession.remainingTimeSeconds, examSession.status]);

  const handleStartExam = async () => {
    try {
      dispatch(setSessionStatus('starting'));
      
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const sessionData = await ExamSessionManager.startSessionAndGetExamData(examId, token, {
        competition_mode: competitionMode,
        event_id: competitionEvent?.id
      });

      if (!sessionData || !sessionData.session_id) {
        throw new Error('Invalid session data received from server');
      }

      dispatch(setSessionId(sessionData.session_id));
      dispatch(setCurrentSessionId(sessionData.session_id));

      const sessionKey = `exam_session_${examId}`;
      localStorage.setItem(sessionKey, JSON.stringify({
        session_id: sessionData.session_id,
        exam_id: examId,
        created_at: new Date().toISOString(),
        status: 'active'
      }));

      if (sessionData.examData && sessionData.examData.total_duration) {
        dispatch(setRemainingTime(sessionData.examData.total_duration * 60));
      }

      dispatch(setSessionStatus('active'));
      dispatch(setConnectionStatus('connected'));
      setExamStarted(true);

      ExamSecurityService.activate({
        onViolation: (violation) => {
          dispatch(addStrike());
        }
      });

    } catch (error) {
      dispatch(setConnectionStatus('error'));
      showError(`Failed to start exam: ${error.message}`);
    }
  };

  const handleAnswerChange = (questionId, answer) => {
    dispatch(updateAnswer({ questionId, answer }));
  };

  const handleSubmitExam = async () => {
    try {
      dispatch(setSessionStatus('submitting'));
      
      const result = await dispatch(submitExamSession({
        sessionId: examSession.sessionId,
        answers: examSession.currentAnswers,
        examData: currentExam
      })).unwrap();

      showSuccess('Exam submitted successfully!');
      navigate('/student/exam-results', { 
        state: { 
          submissionResult: result,
          examId: examId 
        } 
      });
    } catch (error) {
      showError(`Failed to submit exam: ${error.message}`);
      dispatch(setSessionStatus('active'));
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < currentExam.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  // Show error state if there's an error
  if (error) {
    return (
      <ExamLoadingState
        isLoading={false}
        error={error}
        examId={examId}
        onRetry={() => window.location.reload()}
        onBackToExams={() => navigate('/student/exams')}
      />
    );
  }

  // Show loading state only when actively loading
  if (isLoading && examSession.status === 'starting') {
    return (
      <ExamLoadingState
        isLoading={true}
        error={null}
        examId={examId}
        onRetry={() => window.location.reload()}
        onBackToExams={() => navigate('/student/exams')}
      />
    );
  }

  // Pre-exam start screen (show even without exam data initially)
  if (!examStarted || examSession.status !== 'active') {
    return (
      <ExamStartScreen
        exam={currentExam}
        onStartExam={handleStartExam}
        onBackToExams={() => navigate('/student/exams')}
        isStarting={examSession.status === 'starting'}
      />
    );
  }

  // Main exam interface (only show if we have exam data)
  if (!currentExam) {
    return (
      <ExamLoadingState
        isLoading={true}
        error={null}
        examId={examId}
        onRetry={() => window.location.reload()}
        onBackToExams={() => navigate('/student/exams')}
      />
    );
  }

  // Main exam interface
  const currentQuestion = currentExam.questions[currentQuestionIndex];
  
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <ExamHeader
        exam={currentExam}
        remainingTime={examSession.remainingTimeSeconds}
        strikes={examSession.strikes}
        connectionStatus={examSession.connectionStatus}
      />
      
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <ExamQuestion
              question={currentQuestion}
              answer={examSession.currentAnswers[currentQuestion?.id] || ''}
              onAnswerChange={handleAnswerChange}
              questionNumber={currentQuestionIndex + 1}
              totalQuestions={currentExam.questions.length}
            />
          </div>
          
          <div>
            <ExamNavigation
              currentQuestionIndex={currentQuestionIndex}
              totalQuestions={currentExam.questions.length}
              answers={examSession.currentAnswers}
              questions={currentExam.questions}
              onPreviousQuestion={handlePreviousQuestion}
              onNextQuestion={handleNextQuestion}
              onSubmitExam={handleSubmitExam}
              isSubmitting={examSession.status === 'submitting'}
            />
          </div>
        </div>
      </div>
      
      <ConnectionMonitor />
    </div>
  );
}

export default StudentTakeExam;
