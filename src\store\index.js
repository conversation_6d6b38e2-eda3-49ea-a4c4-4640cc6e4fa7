import { configureStore } from '@reduxjs/toolkit';
import loginReducer from './slices/LoginSlice';
import signupReducer from "./slices/SignupSlice";
import userReducer from "./slices/userSlice";
import classroomReducer from "./slices/ClassroomSlice";
import classesReducer from "./slices/ClassesSlice";
import subjectReducer from "./slices/SubjectSlice";
import chapterReducer from "./slices/ChapterSlice";
import topicReducer from "./slices/TopicSlice";
import subtopicReducer from "./slices/SubtopicSlice";
import taskReducer from "./slices/TaskSlice";
import teacherReducer from "./slices/TeacherSlice";
import planReducer from "./slices/PlanSlice";
import announcementReducer from "./slices/AnnouncementSlice";
import teacherSubscriptionReducer from "./slices/TeacherSubscriptionSlice";
import questionReducer from "./slices/QuestionSlice";
import examReducer from "./slices/ExamSlice";
import studentDashboardReducer from "./slices/StudentDashboardSlice";
import studentAnalyticsReducer from "./slices/StudentAnalyticsSlice";
import instituteDashboardReducer from "./slices/InstituteDashboardSlice";
import instituteEventsReducer from "./slices/InstituteEventsSlice";
import instituteMentorsReducer from "./slices/InstituteMentorsSlice";
import instituteReportsReducer from "./slices/InstituteReportsSlice";
import instituteProfileReducer from "./slices/InstituteProfileSlice";
import examSessionReducer from "./slices/exam/examSessionSlice";
import examSessionDataReducer from "./slices/exam/examSessionDataSlice";
import examReconnectionReducer from "./slices/exam/examReconnectionSlice";
import examSessionAdminReducer from "./slices/exam/examSessionAdminSlice";
import aiCheckingReducer from "./slices/exam/aiCheckingSlice";
import subscriptionReducer from "./slices/SubscriptionSlice";
import homeTutoringReducer from "./slices/HomeTutoringSlice";
import eventsReducer from "./slices/EventsSlice";
import mentorsReducer from "./slices/MentorsSlice";
import competitionsReducer from "./slices/CompetitionsSlice";
import emailVerificationReducer from "./slices/EmailVerificationSlice";
import collaborationReducer from "./slices/CollaborationSlice";
import teacherCollaborationReducer from "./slices/TeacherCollaborationSlice";
import paymentReducer from "./slices/PaymentSlice";
import chatReducer from "./slices/chatSlice";

const store = configureStore({
  reducer: {
    login: loginReducer,
    signup: signupReducer,
    users: userReducer,
    classroom: classroomReducer,
    classes: classesReducer,
    subjects: subjectReducer,
    chapters: chapterReducer,
    topics: topicReducer,
    subtopics: subtopicReducer,
    tasks: taskReducer,
    teacher: teacherReducer,
    plans: planReducer,
    announcements: announcementReducer,
    teacherSubscription: teacherSubscriptionReducer,
    questions: questionReducer,
    exams: examReducer,
    studentDashboard: studentDashboardReducer,
    studentAnalytics: studentAnalyticsReducer,
    instituteDashboard: instituteDashboardReducer,
    instituteEvents: instituteEventsReducer,
    instituteMentors: instituteMentorsReducer,
    instituteReports: instituteReportsReducer,
    instituteProfile: instituteProfileReducer,
    examSession: examSessionReducer,
    examSessionData: examSessionDataReducer,
    examReconnection: examReconnectionReducer,
    examSessionAdmin: examSessionAdminReducer,
    aiChecking: aiCheckingReducer,
    subscription: subscriptionReducer,
    homeTutoring: homeTutoringReducer,
    events: eventsReducer,
    mentors: mentorsReducer,
    competitions: competitionsReducer,
    emailVerification: emailVerificationReducer,
    collaboration: collaborationReducer,
    teacherCollaboration: teacherCollaborationReducer,
    payment: paymentReducer,
    chat: chatReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['register', 'rehydrate'],
      },
    }),
  devTools: import.meta.env.MODE !== 'production',
});

export default store;