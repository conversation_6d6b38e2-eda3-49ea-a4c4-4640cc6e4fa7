/* Enhanced Events Page Styles */

/* Improved gradient backgrounds for exam events */
.exam-event-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.exam-event-card-hover {
  background: linear-gradient(135deg, #f8faff 0%, #f3f0ff 100%);
}

/* Enhanced card animations */
.event-card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.event-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Exam button enhancements */
.exam-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.exam-button:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  box-shadow: 0 6px 20px 0 rgba(102, 126, 234, 0.6);
  transform: translateY(-1px);
}

/* Live exam button with pulse animation */
.exam-button.animate-pulse {
  animation: exam-pulse 2s infinite;
}

@keyframes exam-pulse {
  0%, 100% {
    box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.4);
  }
  50% {
    box-shadow: 0 4px 25px 0 rgba(102, 126, 234, 0.8), 0 0 0 4px rgba(102, 126, 234, 0.2);
  }
}

/* Disabled exam button states */
.exam-button-disabled {
  background: #9ca3af;
  cursor: not-allowed;
  opacity: 0.6;
}

.exam-button-pending {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  cursor: not-allowed;
}

/* Status badge improvements */
.status-badge {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Mobile-specific enhancements */
@media (max-width: 768px) {
  .mobile-event-card {
    margin: 0.5rem;
    border-radius: 1rem;
    overflow: hidden;
  }
  
  .mobile-event-actions {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }
  
  .mobile-event-button {
    width: 100%;
    justify-content: center;
    min-height: 44px;
  }
  
  .mobile-event-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .mobile-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

/* Improved focus states for accessibility */
.focus-enhanced:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
  border-radius: 0.5rem;
}

/* Loading skeleton for events */
.event-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 0.75rem;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced filter section */
.filter-section {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

/* Improved spacing for event details */
.event-detail-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

@media (max-width: 640px) {
  .event-detail-grid {
    grid-template-columns: 1fr;
  }
}

/* Enhanced timing badges */
.timing-badge-glow {
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.05);
}

/* Improved button groups */
.button-group {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

@media (max-width: 640px) {
  .button-group {
    flex-direction: column;
    width: 100%;
  }
  
  .button-group > * {
    width: 100%;
  }
}

/* Enhanced section dividers */
.section-divider {
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  height: 1px;
  margin: 2rem 0;
}

/* Improved empty state styling */
.empty-state {
  background: radial-gradient(circle at center, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
  border: 2px dashed rgba(59, 130, 246, 0.2);
  border-radius: 1rem;
  padding: 3rem 2rem;
  text-align: center;
}

/* Enhanced search input */
.search-input-enhanced {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.search-input-enhanced:focus {
  background: rgba(255, 255, 255, 1);
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Responsive text scaling for events */
@media (max-width: 640px) {
  .event-title-responsive {
    font-size: 1.125rem;
    line-height: 1.5;
  }
  
  .event-subtitle-responsive {
    font-size: 0.875rem;
    line-height: 1.4;
  }
}

/* Enhanced hover effects for interactive elements */
.interactive-hover {
  transition: all 0.2s ease;
}

.interactive-hover:hover {
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: 0.5rem;
}

/* Improved visual hierarchy */
.visual-hierarchy-1 {
  font-weight: 700;
  color: #1f2937;
}

.visual-hierarchy-2 {
  font-weight: 600;
  color: #374151;
}

.visual-hierarchy-3 {
  font-weight: 500;
  color: #6b7280;
}

/* Enhanced card shadows */
.card-shadow-soft {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.card-shadow-medium {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-shadow-strong {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
